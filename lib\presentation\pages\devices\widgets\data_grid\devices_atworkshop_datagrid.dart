import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/data_sources/atworkshop_data_source.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';
import 'package:flutter/services.dart';

class DevicesAtworkshopDataGrid extends ConsumerStatefulWidget {
  const DevicesAtworkshopDataGrid({super.key});

  @override
  ConsumerState<DevicesAtworkshopDataGrid> createState() =>
      _DevicesAtworkshopDataGridState();
}

class _DevicesAtworkshopDataGridState
    extends ConsumerState<DevicesAtworkshopDataGrid> {
  late DataGridController _dataGridController;

  @override
  void initState() {
    super.initState();
    _dataGridController = DataGridController();
  }

  @override
  void dispose() {
    _dataGridController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    final lang = AppLocalizations.of(context);
    final deviceState = ref.watch(deviceProvider('devicesAtWorkshop'));
    final notifier = ref.read(deviceProvider('devicesAtWorkshop').notifier);
    // final selectedDevice = ref.read(selectedDeviceProvider);
    final dataSource = DevicesAtworkshopDataSource(
      context,
      devices: deviceState.devices,
      ref: ref,
      // selectedDevice: selectedDevice,
    );
    void handleRowSelection(DataGridRow row) {
      final device = deviceState.devices.firstWhere(
        (d) => d.machineId == row.getCells()[0].value,
      );
      ref.read(selectedDeviceProvider.notifier).state = device;
    }

    FocusNode dataGridFocusNode = FocusNode();

    void handleArrowKeyNavigation(KeyEvent event) {
      if (deviceState.devices.isEmpty) return;
      if (event is! KeyDownEvent) return;
      final selected = ref.read(selectedDeviceProvider);
      int currentIndex =
          selected == null
              ? -1
              : deviceState.devices.indexWhere(
                (d) => d.machineId == selected.machineId,
              );

      if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
        int nextIndex = (currentIndex + 1).clamp(
          0,
          deviceState.devices.length - 1,
        );
        if (nextIndex != currentIndex &&
            nextIndex < deviceState.devices.length) {
          ref.read(selectedDeviceProvider.notifier).state =
              deviceState.devices[nextIndex];
        }
      } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
        int prevIndex = (currentIndex - 1).clamp(
          0,
          deviceState.devices.length - 1,
        );
        if (prevIndex != currentIndex && prevIndex >= 0) {
          ref.read(selectedDeviceProvider.notifier).state =
              deviceState.devices[prevIndex];
        }
      }
    }

    if (deviceState.isLoading) {
      return Center(child: ProgressRing());
    }

    // Show "no data yet" if there are no devices
    if (deviceState.devices.isEmpty) {
      return Center(
        child: Text(
          'NO DATA',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: const Color.fromARGB(255, 97, 96, 96),
          ),
        ),
      );
    }

    return Focus(
      focusNode: dataGridFocusNode,
      autofocus: true,
      onKeyEvent: (FocusNode node, KeyEvent event) {
        handleArrowKeyNavigation(event);
        return KeyEventResult.handled;
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 0,
        children: [
          Expanded(
            flex: 1,
            child: SfDataGridTheme(
              data: SfDataGridThemeData(
                headerColor: Colors.blue,
                selectionColor: Colors.blue.lightest,
              ),
              child: ClipRect(
                // clipBehavior: Clip.values.first,
                clipper: DataGridClipper(),
                child: ScrollConfiguration(
                  behavior: const ScrollBehavior().copyWith(scrollbars: false),
                  child: SfDataGrid(
                    controller: _dataGridController,
                    headerGridLinesVisibility: GridLinesVisibility.none,
                    gridLinesVisibility: GridLinesVisibility.horizontal,
                    source: dataSource,
                    columnWidthMode: ColumnWidthMode.fill,
                    columnResizeMode: ColumnResizeMode.onResize,
                    rowHeight: 60.0,
                    headerRowHeight: 50.0,
                    isScrollbarAlwaysShown: false,
                    selectionMode: SelectionMode.single,
                    onSelectionChanged: (addedRows, removedRows) {
                      if (addedRows.isNotEmpty) {
                        handleRowSelection(addedRows.first);
                      }
                    },
                    columns: <GridColumn>[
                      GridColumn(
                        columnName: 'id',
                        label: Container(
                          padding: EdgeInsets.all(16.0),
                          alignment: Alignment.center,
                          child: Text(lang.id, style: headerTextStyle),
                        ),
                      ),
                      GridColumn(
                        columnName: 'client',
                        label: Container(
                          padding: EdgeInsets.all(16.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.client.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'type',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.type.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'brand',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.brand.toUpperCase(),
                            style: headerTextStyle,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'serei',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.serie.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'model',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.model.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'serial_number',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.serial_n.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'issue',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.issue.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'deadline',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.deadline.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'phase',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.phase.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'user',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.user.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'date',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.date.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SfDataPagerTheme(
            data: SfDataPagerThemeData(
              backgroundColor: FluentTheme.of(
                context,
              ).resources.controlStrokeColorSecondary.withAlpha(10),
            ),
            child: SfDataPager(
              delegate: DevicesDataPagerDelegate(
                notifier,
                deviceState,
                _dataGridController,
              ),
              pageCount:
                  DevicesDataPagerDelegate(
                    notifier,
                    deviceState,
                    _dataGridController,
                  ).pageCount.toDouble(),
              direction: Axis.horizontal,
            ),
          ),
        ],
      ),
    );
  }
}

class DevicesDataPagerDelegate extends DataPagerDelegate {
  final DeviceNotifier notifier;
  final DeviceState state;
  final DataGridController dataGridController;
  DevicesDataPagerDelegate(this.notifier, this.state, this.dataGridController);

  @override
  Future<bool> handlePageChange(int oldPageIndex, int newPageIndex) async {
    await notifier.setPage(newPageIndex);

    // Reset scroll position to top when page changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      dataGridController.scrollToRow(0);
    });

    return true;
  }

  int get rowCount => state.totalCount;

  int get pageSize => 50;

  bool get shouldRecalculatePageCount => true;

  int get pageCount => (rowCount / pageSize).ceil();
}
