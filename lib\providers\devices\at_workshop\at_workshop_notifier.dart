import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:workshop_studio/models/device/device_model.dart';
import 'package:workshop_studio/providers/clients/search/search_query_notifier.dart';
import 'package:workshop_studio/providers/clients/timeline_filter_provider.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/services/database_service.dart';

// Phase filter provider for devices at workshop
final phaseFilterProvider = StateProvider.family<int?, String>(
  (ref, uid) => null,
);

// Current page provider for devices at workshop
final currentPageProvider = StateProvider.family<int, String>((ref, uid) => 0);

class DeviceState {
  final List<DeviceModel> devices;
  bool isLoading;
  int totalCount;
  int currentPage;

  DeviceState({
    required this.devices,
    required this.isLoading,
    required this.totalCount,
    required this.currentPage,
  });

  DeviceState copyWith({
    List<DeviceModel>? devices,
    bool? isLoading,
    int? totalCount,
    int? currentPage,
  }) {
    return DeviceState(
      devices: devices ?? this.devices,
      isLoading: isLoading ?? this.isLoading,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

final selectedDeviceProvider = StateProvider<DeviceModel?>((ref) => null);

class DeviceNotifier extends StateNotifier<DeviceState> {
  final MySQLService _dbService;
  final TimelineFilter filter;
  final int _limit = 50;
  final String searchQuery;
  final int? phaseFilter;
  final Ref ref; // Add ref to update loading state provider

  DeviceNotifier(
    this._dbService,
    this.filter,
    this.searchQuery,
    this.phaseFilter,
    this.ref,
  ) : super(
        DeviceState(
          devices: [],
          isLoading: true,
          totalCount: 0,
          currentPage: 0,
        ),
      ) {
    loadDevices();
  }

  Future<void> loadDevices() async {
    // Build base query conditions
    String whereClause = "WHERE client_machines.delivery = 0";
    Map<String, dynamic> params = {};

    final isSearching = searchQuery.isNotEmpty;

    if (!isSearching && filter.type != TimelineFilterType.all) {
      String fromDate = "";
      String toDate = "";
      final now = DateTime.now();

      if (filter.type == TimelineFilterType.currentYear) {
        fromDate = DateFormat('yyyy-01-01').format(now);
        toDate = DateFormat('yyyy-12-31').format(now);
      } else if (filter.type == TimelineFilterType.currentMonth) {
        fromDate = DateFormat('yyyy-MM-01').format(now);
        toDate = DateFormat(
          'yyyy-MM-dd',
        ).format(DateTime(now.year, now.month + 1, 0));
      } else if (filter.type == TimelineFilterType.currentWeek) {
        final beginningOfWeek = now.subtract(Duration(days: now.weekday - 1));
        final endOfWeek = beginningOfWeek.add(const Duration(days: 6));
        fromDate = DateFormat('yyyy-MM-dd').format(beginningOfWeek);
        toDate = DateFormat('yyyy-MM-dd').format(endOfWeek);
      } else if (filter.type == TimelineFilterType.customRange &&
          filter.from != null &&
          filter.to != null) {
        fromDate = DateFormat('yyyy-MM-dd').format(filter.from!);
        toDate = DateFormat('yyyy-MM-dd').format(filter.to!);
      }

      whereClause =
          "WHERE client_machines.date BETWEEN :from AND :to AND client_machines.delivery = 0";
      params["from"] = fromDate;
      params["to"] = toDate;
    }

    if (isSearching) {
      // Add search for client_machines.b_id (as string)
      whereClause = """
      WHERE client_machines.delivery = 0 AND (
        client_machines.m_type LIKE :query OR
        client_machines.m_brand LIKE :query OR
        client_machines.m_serie LIKE :query OR
        client_machines.m_model LIKE :query OR
        client_machines.m_serial_num LIKE :query OR
        client_machines.m_problem LIKE :query OR
        client_machines.technician LIKE :query OR
        CAST(client_machines.b_id AS CHAR) LIKE :query OR
        clients.c_name LIKE :query
      )
      """;
      params["query"] = '%$searchQuery%';
    }

    // Add phase filter if specified
    if (phaseFilter != null) {
      if (whereClause.contains("WHERE")) {
        whereClause += " AND client_machines.phase = :phase";
      } else {
        whereClause =
            "WHERE client_machines.delivery = 0 AND client_machines.phase = :phase";
      }
      params["phase"] = phaseFilter;
    }

    // Get total count
    final countResult = await _dbService.query("""
        SELECT COUNT(*) AS total 
        FROM client_machines
        JOIN clients ON client_machines.c_id = clients.id
        $whereClause
      """, params: params);
    final totalCount = int.parse(countResult.rows.first.colByName('total')!);

    // Get paginated data
    final dataQuery = """
      SELECT client_machines.*, clients.c_name AS c_name , clients.c_phone AS c_phone 
      FROM client_machines JOIN clients ON client_machines.c_id = clients.id 
      $whereClause 
      ORDER BY client_machines.m_id DESC 
      LIMIT $_limit OFFSET ${state.currentPage * _limit}
    """;
    final dataResult = await _dbService.query(dataQuery, params: params);
    final devices =
        dataResult.rows.map((row) {
          return DeviceModel.fromJson({
            'm_id': row.colByName('m_id')!,
            'c_name': row.colByName('c_name')!,
            'c_phone': row.colByName('c_phone')!,
            'c_id': row.colByName('c_id')!,
            'b_id': row.colByName('b_id')!,
            'm_type': row.colByName('m_type') ?? '',
            'm_brand': row.colByName('m_brand') ?? '',
            'm_serie': row.colByName('m_serie') ?? '',
            'm_model': row.colByName('m_model') ?? '',
            'm_serial_num': row.colByName('m_serial_num') ?? '',
            'm_problem': row.colByName('m_problem') ?? '',
            'm_estimated_price': row.colByName('m_estimated_price') ?? 0.00,
            'price': row.colByName('price') ?? 0.00,
            'warranty': row.colByName('warranty') ?? 0,
            'emergency': row.colByName('emergency') ?? 0,
            'technician': row.colByName('technician') ?? '',
            'deadline': row.colByName('deadline') ?? 0,
            'note': row.colByName('note') ?? '',
            'date': row.colByName('date') ?? '',
            'time': row.colByName('time') ?? '',
            'phase': row.colByName('phase')!,
            'dateupdate': row.colByName('dateupdate') ?? '',
            'delivery': row.colByName('delivery') ?? 0,
            'datedelivery': row.colByName('datedelivery') ?? '',
            'calculated': row.colByName('calculated') ?? 0,
          });
        }).toList();
    state = state.copyWith(
      devices: devices,
      isLoading: false,
      totalCount: totalCount,
    );
  }

  Future<void> setPage(int page) async {
    if (state.currentPage == page) return;
    state = state.copyWith(currentPage: page);
    await loadDevices();
  }

  Future<void> reset() async {
    // Keep the current page number when resetting (don't reset currentPage to 0)
    state = state.copyWith(
      devices: [],
      currentPage: state.currentPage,
      totalCount: 0,
    );
    ref.read(selectedDeviceProvider.notifier).state = null;
    // Reload devices for the current page (loadDevices uses state.currentPage for pagination)
    await loadDevices();
  }

  /// Remove a specific device from the current list without full refresh
  void removeDeviceFromList(int machineId) {
    final updatedDevices =
        state.devices.where((device) => device.machineId != machineId).toList();
    final newTotalCount = state.totalCount > 0 ? state.totalCount - 1 : 0;

    state = state.copyWith(devices: updatedDevices, totalCount: newTotalCount);

    // Clear selected device if it was the one being deleted
    final selectedDevice = ref.read(selectedDeviceProvider);
    if (selectedDevice?.machineId == machineId) {
      ref.read(selectedDeviceProvider.notifier).state = null;
    }
  }

  /// Fetch technician assigned to a specific machine
  Future<String?> getTechnicianForDevice(int machineId) async {
    try {
      final result = await _dbService.query(
        """
        SELECT CONCAT(workers.first_name, ' ', workers.last_name) AS technician_name 
        FROM workers 
        INNER JOIN activity ON workers.id = activity.w_id 
        WHERE activity.m_id = :machineId
        """,
        params: {'machineId': machineId},
      );

      if (result.rows.isNotEmpty) {
        return result.rows.first.colByName('technician_name');
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Fetch all technicians assigned to a specific machine (in case multiple technicians work on one machine)
  Future<List<Map>> getAllTechnicians() async {
    try {
      final result = await _dbService.query("""
        SELECT id, CONCAT(workers.first_name, ' ', workers.last_name) AS technician_name
        FROM workers
      """);

      final List<Map> technicians = [];
      for (final row in result.rows) {
        final id = row.colByName('id');
        final name = row.colByName('technician_name') ?? '';
        if (name.isNotEmpty && id != null) {
          technicians.add({'id': id, 'name': name});
        }
      }
      return technicians;
    } catch (e) {
      return [];
    }
  }
}

final deviceProvider = StateNotifierProvider.family<
  DeviceNotifier,
  DeviceState,
  String
>((ref, uid) {
  final db = ref.read(mysqlServiceProvider);
  final filter = ref.watch(timelineFilterProviderFamily('devicesAtWorkshop'));
  final searchQuery = ref.watch(searchQueryProvider('devicesAtWorkshop'));
  final phaseFilter = ref.watch(phaseFilterProvider('devicesAtWorkshop'));
  return DeviceNotifier(db, filter, searchQuery, phaseFilter, ref);
});
