import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/device/device_model.dart';
import 'package:workshop_studio/presentation/utils/upper_case_formatter.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_crud_provider.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';
import 'package:workshop_studio/providers/devices/device_suggestions_provider.dart';

void showDeviceEditDialog(BuildContext context, WidgetRef ref) {
  final selectedDevice = ref.read(selectedDeviceProvider);
  if (selectedDevice == null) return;

  showDialog(
    context: context,
    builder: (context) => DeviceEditDialog(device: selectedDevice, ref: ref),
  );
}

class DeviceEditDialog extends StatefulWidget {
  final DeviceModel device;
  final WidgetRef ref;

  const DeviceEditDialog({super.key, required this.device, required this.ref});

  @override
  State<DeviceEditDialog> createState() => _DeviceEditDialogState();
}

class _DeviceEditDialogState extends State<DeviceEditDialog> {
  late TextEditingController typeController;
  late TextEditingController brandController;
  late TextEditingController serieController;
  late TextEditingController modelController;
  late TextEditingController serialController;
  late TextEditingController issueController;
  late TextEditingController estimatedPriceController;
  late TextEditingController deadlineController;
  late TextEditingController noteController;

  bool warranty = false;
  bool emergency = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with current device data
    typeController = TextEditingController(text: widget.device.machineType);
    brandController = TextEditingController(text: widget.device.machineBrand);
    serieController = TextEditingController(
      text: widget.device.machineSerie ?? '',
    );
    modelController = TextEditingController(
      text: widget.device.machineModel ?? '',
    );
    serialController = TextEditingController(
      text: widget.device.serialNumber ?? '',
    );
    issueController = TextEditingController(text: widget.device.problem ?? '');
    estimatedPriceController = TextEditingController(
      text: widget.device.estimatedPrice.toStringAsFixed(2),
    );
    deadlineController = TextEditingController(
      text: widget.device.deadline.toString(),
    );
    noteController = TextEditingController(text: widget.device.note ?? '');

    // Initialize checkboxes
    warranty = widget.device.warranty == 1;
    emergency = widget.device.emergency == 1;
  }

  @override
  void dispose() {
    typeController.dispose();
    brandController.dispose();
    serieController.dispose();
    modelController.dispose();
    serialController.dispose();
    issueController.dispose();
    estimatedPriceController.dispose();
    deadlineController.dispose();
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final dialogWidth = MediaQuery.of(context).size.width / 3;
    final dialogHeight = MediaQuery.of(context).size.height / 1.6;
    final deviceCrud = widget.ref.read(deviceCrudProvider('devicesAtWorkshop'));

    return ContentDialog(
      constraints: BoxConstraints.expand(
        width: dialogWidth,
        height: dialogHeight,
      ),
      title: Row(
        children: [
          Icon(CarbonIcons.edit, size: 20),
          SizedBox(width: 8),
          Text('${lang.edit} ${lang.device}'),
        ],
      ),
      content: SizedBox(
        width: 600,
        height: 500,
        child: Consumer(
          builder: (context, ref, child) {
            final suggestionsAsync = ref.watch(deviceSuggestionsProvider);
            return suggestionsAsync.when(
              data:
                  (suggestions) => Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: CardHighlight(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              spacing: 16,
                              children: [
                                // First row: Type and Issue
                                Row(
                                  spacing: 16,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.type}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          AutoSuggestBox<String>(
                                            controller: typeController,
                                            placeholder: lang.type,
                                            inputFormatters: [
                                              UpperCaseTextFormatter(),
                                            ],
                                            items:
                                                suggestions['types']!.map((
                                                  type,
                                                ) {
                                                  return AutoSuggestBoxItem<
                                                    String
                                                  >(
                                                    value: type,
                                                    label: type,
                                                    onFocusChange: (focused) {
                                                      if (focused) {
                                                        debugPrint(
                                                          'Focused $type',
                                                        );
                                                      }
                                                    },
                                                  );
                                                }).toList(),
                                            onSelected: (item) {
                                              setState(() {
                                                typeController.text =
                                                    item.value!.toUpperCase();
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.issue}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          AutoSuggestBox<String>(
                                            controller: issueController,
                                            placeholder: lang.issue,
                                            inputFormatters: [
                                              UpperCaseTextFormatter(),
                                            ],
                                            items:
                                                suggestions['problems']!
                                                    .map(
                                                      (problem) =>
                                                          AutoSuggestBoxItem<
                                                            String
                                                          >(
                                                            value: problem,
                                                            label: problem,
                                                            child: Text(
                                                              problem,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              maxLines: 1,
                                                              style: TextStyle(
                                                                fontSize: 12,
                                                              ),
                                                            ),
                                                          ),
                                                    )
                                                    .toList(),
                                            onSelected: (item) {
                                              setState(() {
                                                issueController.text =
                                                    item.value!.toUpperCase();
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                // Second row: Brand and Deadline
                                Row(
                                  spacing: 16,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.brand}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          AutoSuggestBox<String>(
                                            controller: brandController,
                                            placeholder: lang.brand,
                                            inputFormatters: [
                                              UpperCaseTextFormatter(),
                                            ],
                                            items:
                                                suggestions['brands']!.map((
                                                  brand,
                                                ) {
                                                  return AutoSuggestBoxItem<
                                                    String
                                                  >(
                                                    value: brand,
                                                    label: brand,
                                                    onFocusChange: (focused) {
                                                      if (focused) {
                                                        debugPrint(
                                                          'Focused $brand',
                                                        );
                                                      }
                                                    },
                                                  );
                                                }).toList(),
                                            onSelected: (item) {
                                              setState(() {
                                                brandController.text =
                                                    item.value!.toUpperCase();
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.deadline}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          NumberBox<int>(
                                            value: int.tryParse(
                                              deadlineController.text,
                                            ),
                                            onChanged: (value) {
                                              deadlineController.text =
                                                  value?.toString() ?? '0';
                                            },
                                            min: 0,
                                            max: 365,
                                            placeholder: lang.deadline,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                // Third row: Serie and Estimated Price
                                Row(
                                  spacing: 16,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.serie}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          AutoSuggestBox<String>(
                                            controller: serieController,
                                            placeholder: lang.serie,
                                            inputFormatters: [
                                              UpperCaseTextFormatter(),
                                            ],
                                            items:
                                                suggestions['series']!.map((
                                                  serie,
                                                ) {
                                                  return AutoSuggestBoxItem<
                                                    String
                                                  >(
                                                    value: serie,
                                                    label: serie,
                                                    onFocusChange: (focused) {
                                                      if (focused) {
                                                        debugPrint(
                                                          'Focused $serie',
                                                        );
                                                      }
                                                    },
                                                  );
                                                }).toList(),
                                            onSelected: (item) {
                                              setState(() {
                                                serieController.text =
                                                    item.value!.toUpperCase();
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.estimated_price}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          TextBox(
                                            controller:
                                                estimatedPriceController,
                                            placeholder: lang.estimated_price,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                RegExp(r'^\d+\.?\d{0,2}'),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                // Fourth row: Model and Checkboxes
                                Row(
                                  spacing: 16,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.model}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          AutoSuggestBox<String>(
                                            controller: modelController,
                                            placeholder: lang.model,
                                            inputFormatters: [
                                              UpperCaseTextFormatter(),
                                            ],
                                            items:
                                                suggestions['models']!.map((
                                                  model,
                                                ) {
                                                  return AutoSuggestBoxItem<
                                                    String
                                                  >(
                                                    value: model,
                                                    label: model,
                                                    onFocusChange: (focused) {
                                                      if (focused) {
                                                        debugPrint(
                                                          'Focused $model',
                                                        );
                                                      }
                                                    },
                                                  );
                                                }).toList(),
                                            onSelected: (item) {
                                              setState(() {
                                                modelController.text =
                                                    item.value!.toUpperCase();
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${lang.serial_n}:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue.lightest,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          TextBox(
                                            controller: serialController,
                                            placeholder: lang.serial_n,
                                            inputFormatters: [
                                              UpperCaseTextFormatter(),
                                            ],
                                            prefix: Padding(
                                              padding: EdgeInsets.all(8),
                                              child: Icon(
                                                CarbonIcons.barcode,
                                                size: 16,
                                              ),
                                            ),
                                            suffix: IconButton(
                                              icon: Icon(
                                                FluentIcons.clear,
                                                size: 12,
                                              ),
                                              onPressed:
                                                  () =>
                                                      serialController.clear(),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),

                                // Fifth row: Warranty and Emergency
                                Row(
                                  spacing: 16,
                                  children: [
                                    Expanded(
                                      child: Checkbox(
                                        checked: warranty,
                                        onChanged:
                                            (value) => setState(
                                              () => warranty = value ?? false,
                                            ),
                                        content: Text(lang.warranty),
                                      ),
                                    ),
                                    Expanded(
                                      child: Checkbox(
                                        checked: emergency,
                                        onChanged:
                                            (value) => setState(
                                              () => emergency = value ?? false,
                                            ),
                                        content: Text(lang.emergency),
                                      ),
                                    ),
                                  ],
                                ),

                                // Note section
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${lang.remarks}:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        color: Colors.blue.lightest,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    TextBox(
                                      controller: noteController,
                                      placeholder: '${lang.remarks}...',
                                      inputFormatters: [
                                        UpperCaseTextFormatter(),
                                      ],
                                      maxLines: 4,
                                      minLines: 4,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              loading:
                  () => Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: CardHighlight(
                          child: Center(child: ProgressRing()),
                        ),
                      ),
                    ],
                  ),
              error:
                  (error, stack) => Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: CardHighlight(
                          child: Center(
                            child: Text('Error loading suggestions'),
                          ),
                        ),
                      ),
                    ],
                  ),
            );
          },
        ),
      ),
      actions: [
        Button(
          onPressed: () => Navigator.pop(context),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(CarbonIcons.exit, size: 16),
              SizedBox(width: 4),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(lang.cancel),
              ),
            ],
          ),
        ),
        SizedBox(width: 8),
        FilledButton(
          onPressed: isLoading ? null : () => _saveChanges(deviceCrud, lang),
          child: Padding(
            padding: EdgeInsets.all(isLoading ? 2.0 : 8.0),
            child:
                isLoading
                    ? SizedBox(
                      width: 16,
                      height: 16,
                      child: ProgressRing(strokeWidth: 2),
                    )
                    : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(CarbonIcons.edit, size: 16),
                        SizedBox(width: 4),
                        Text(lang.edit),
                      ],
                    ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveChanges(
    DeviceCRUD deviceCrud,
    AppLocalizations lang,
  ) async {
    // Validate required fields
    if (typeController.text.trim().isEmpty) {
      _showErrorMessage(lang, '${lang.enter} ${lang.type}');
      return;
    }

    if (brandController.text.trim().isEmpty) {
      _showErrorMessage(lang, '${lang.enter} ${lang.brand}');
      return;
    }

    final estimatedPrice = double.tryParse(estimatedPriceController.text);
    if (estimatedPrice == null || estimatedPrice < 0) {
      _showErrorMessage(
        lang,
        '${lang.enter} ${lang.valid} ${lang.estimated_price}',
      );
      return;
    }

    final deadline = int.tryParse(deadlineController.text);
    if (deadline == null || deadline < 0) {
      _showErrorMessage(lang, '${lang.enter} ${lang.valid} ${lang.deadline}');
      return;
    }

    setState(() => isLoading = true);

    try {
      final success = await deviceCrud.editDevice(
        machineId: widget.device.machineId,
        machineType: typeController.text.trim(),
        machineBrand: brandController.text.trim(),
        machineSerie:
            serieController.text.trim().isEmpty
                ? null
                : serieController.text.trim(),
        machineModel:
            modelController.text.trim().isEmpty
                ? null
                : modelController.text.trim(),
        serialNumber:
            serialController.text.trim().isEmpty
                ? null
                : serialController.text.trim(),
        problem:
            issueController.text.trim().isEmpty
                ? null
                : issueController.text.trim(),
        estimatedPrice: estimatedPrice,
        warranty: warranty,
        emergency: emergency,
        deadline: deadline,
        note:
            noteController.text.trim().isEmpty
                ? null
                : noteController.text.trim(),
      );

      setState(() => isLoading = false);

      if (success) {
        // Update the selected device with new data
        final updatedDevice = widget.device.copyWith(
          machineType: typeController.text.trim(),
          machineBrand: brandController.text.trim(),
          machineSerie:
              serieController.text.trim().isEmpty
                  ? null
                  : serieController.text.trim(),
          machineModel:
              modelController.text.trim().isEmpty
                  ? null
                  : modelController.text.trim(),
          serialNumber:
              serialController.text.trim().isEmpty
                  ? null
                  : serialController.text.trim(),
          problem:
              issueController.text.trim().isEmpty
                  ? null
                  : issueController.text.trim(),
          estimatedPrice: estimatedPrice,
          warranty: warranty ? 1 : 0,
          emergency: emergency ? 1 : 0,
          deadline: deadline,
          note:
              noteController.text.trim().isEmpty
                  ? null
                  : noteController.text.trim(),
        );
        widget.ref.read(selectedDeviceProvider.notifier).state = updatedDevice;

        // Refresh the device list
        widget.ref
            .read(deviceProvider('devicesAtWorkshop').notifier)
            .loadDevices();

        if (mounted) {
          Navigator.of(context).pop();
          _showSuccessMessage(lang);
        }
      } else {
        _showErrorMessage(lang, lang.failedToUpdateDevice);
      }
    } catch (e) {
      setState(() => isLoading = false);
      _showErrorMessage(lang, '${lang.error}: ${e.toString()}');
    }
  }

  void _showSuccessMessage(AppLocalizations lang) {
    if (!mounted) return;
    displayInfoBar(
      context,
      builder: (context, close) {
        return InfoBar(
          title: Text(lang.ok),
          content: Text(lang.deviceUpdatedSuccessfully),
          severity: InfoBarSeverity.success,
        );
      },
    );
  }

  void _showErrorMessage(AppLocalizations lang, String message) {
    if (!mounted) return;
    displayInfoBar(
      context,
      builder: (context, close) {
        return InfoBar(
          title: Text(lang.error),
          content: Text(message),
          severity: InfoBarSeverity.error,
        );
      },
    );
  }
}
